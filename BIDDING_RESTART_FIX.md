# Bidding Restart Fix - Game Flow Issue Resolution

## Problem Description

**Issue**: After a game over, when the bidding dialog opens again, the game restarts infinitely.

**Root Cause**: The `allPlayersPassedInFirstRound()` function was being triggered every time all players passed, including:
1. True first round scenarios (legitimate restart case)
2. Subsequent deals after game completion (should NOT restart)

This created an infinite loop where:
1. Game ends → New deal starts
2. All players pass → System restarts bidding
3. All players pass again → System restarts bidding again
4. This continues indefinitely

## Solution Implemented

### 1. Enhanced Bidding Round Tracking

**Added new function**: `allPlayersPassedInCurrentRound()`
```typescript
export const allPlayersPassedInCurrentRound = (bids: Bid[], playerCount: number, currentBiddingRound: number): boolean => {
  const currentRoundBids = bids.filter(bid => bid.bidRound === currentBiddingRound);
  if (currentRoundBids.length !== playerCount) return false;
  return currentRoundBids.every(bid => bid.isPassed);
};
```

This function only checks bids from the current bidding round, not all historical bids.

### 2. Bidding Restart Counter

**Added field to GameRoom interface**: `biddingRestartCount?: number`

This tracks how many times bidding has been restarted for the current deal.

### 3. Maximum Restart Limit

**Implemented restart limits**:
- Maximum of 2 bidding restarts per deal
- After reaching the limit, the system progresses to the next dealer instead of restarting

### 4. Enhanced Logic in `passBid()` Function

```typescript
if (allPlayersPassedInCurrentRound(newBids, roomData.players.length, roomData.biddingRound)) {
  const restartCount = roomData.biddingRestartCount || 0;
  const maxRestarts = 2;
  
  if (restartCount < maxRestarts) {
    // Restart bidding (limited number of times)
    await updateDoc(roomRef, {
      bids: [],
      currentBid: 150,
      highestBidder: null,
      biddingTurn: roomData.turnOrder[0],
      biddingRound: roomData.biddingRound + 1,
      biddingRestartCount: restartCount + 1,
      updatedAt: serverTimestamp()
    });
    return;
  } else {
    // Too many restarts - progress to next dealer
    await this.progressDeal(roomId);
    return;
  }
}
```

### 5. Reset Counter for New Deals

**Updated `progressDeal()` and `startGame()`**:
- `biddingRestartCount: 0` is set for each new deal
- Ensures clean slate for each deal

### 6. Comprehensive State Reset

**Enhanced `progressDeal()` to clear all trump-related fields**:
```typescript
trumpSuit: null,
trumpCard: null,
trumpIndicatorCardId: null,
trumpGameType: null,
trumpSpecialRules: null,
trumpRevealCondition: null,
isTrumpRevealed: false,
isTrumpSuitAnnounced: false,
canInspectFaceDown: false,
trumpRevealCount: 0,
trumpHistory: [],
originalTrumpSuit: null,
alternativeTrumpSuits: null,
hasDistributedRemainingCards: false,
highestBidder: null,
```

### 7. Remaining Deck Management

**Fixed "Remaining deck not found" error**:
- Properly create new remaining deck for each deal
- Clean up old remaining deck entries
- Prevent deck not found errors during trump card selection

## Files Modified

1. **`services/gameService.ts`**:
   - Added `allPlayersPassedInCurrentRound()` function
   - Enhanced `passBid()` logic with restart limits
   - Updated `progressDeal()` to reset all relevant fields
   - Updated `startGame()` to initialize restart counter
   - Added comprehensive state cleanup

## Benefits

1. **Prevents infinite restarts**: Game now has a maximum limit on bidding restarts
2. **Proper game flow**: After reaching restart limit, game progresses naturally
3. **Clean state management**: Each deal starts with completely reset state
4. **Better error handling**: Remaining deck management prevents trump selection errors
5. **Maintains game rules**: Still allows legitimate bidding restarts (up to limit)

## Testing Scenarios

The fix handles these scenarios correctly:

1. ✅ **Normal bidding flow**: Works as before
2. ✅ **First round all pass**: Restarts bidding (up to limit)
3. ✅ **Multiple all-pass rounds**: Limited restarts, then progresses
4. ✅ **Game completion**: Clean transition to new deal
5. ✅ **Trump selection**: No "remaining deck not found" errors
6. ✅ **Infinite restart prevention**: Game doesn't get stuck

## Configuration

- **Maximum restarts**: 2 per deal (configurable in code)
- **Default behavior**: After max restarts, progress to next dealer
- **Backward compatibility**: Existing game logic unchanged

This fix ensures stable game flow while maintaining the authentic 304 game experience. 