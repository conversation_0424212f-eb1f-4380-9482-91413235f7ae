# 304 Card Playing and Trick Logic - Rules Implementation Update

## Overview

This document outlines the changes made to implement the missing card playing and trick logic rules as specified in the official 304 documentation, focusing on **Exhausted Trump Rule**, **Enhanced Face-Down Card Logic**, and **Communication Restrictions**.

## Changes Made

### 1. **Exhausted Trump Rule for 250+ Bids**

**New Rule Implementation:**
```typescript
// 304 RULE: When trumps are exhausted, trump maker MUST lead all remaining trumps
export const checkExhaustedTrumps = (gameRoom: GameRoom, trumpMaker: Player) => {
  // Checks if trump maker has ALL remaining trump cards
  // Returns: { isExhausted, remainingTrumps, mustLeadAll }
}

export const validateExhaustedTrumpPlay = (cardToPlay, gameRoom, playerId, isLeading) => {
  // Validates card play according to exhausted trump rule
  // Prevents playing non-trump cards when trump maker has remaining trumps
}
```

**Key Features:**
- **Automatic Detection**: System automatically detects when trump maker has all remaining trump cards
- **Enforcement**: Trump maker MUST lead all remaining trumps before playing other cards
- **250+ Bid Trigger**: Rule only applies to bids of 250 or higher
- **Trump Indicator Exception**: Trump indicator card cannot be led except in last trick

### 2. **Enhanced Face-Down Card Logic for 250+ Bids**

**Rule Enhancement:**
```typescript
// 304 RULE: For 250+ bids, face-down cards only in first trick
export const shouldPlayFaceDownEnhanced = (playedCards, cardToPlay, playerCards, isTrumpRevealed, trumpGameType, gameRoom) => {
  if (gameRoom && is250Plus) {
    const tricksCompleted = Math.floor(playedCards.length / gameRoom.players.length);
    if (tricksCompleted > 0) {
      return false; // After first trick, all cards played face-up for 250+ bids
    }
  }
  return originalLogic;
}
```

**Key Features:**
- **First Trick Only**: For 250+ bids, face-down cards only allowed in first trick
- **Auto-Reveal After First Trick**: Trump automatically revealed after first trick for 250+ bids
- **Backwards Compatibility**: Original face-down logic preserved for lower bids

### 3. **Communication Restrictions During Play**

**New Interface:**
```typescript
export interface SpecialBidRules {
  is250Plus: boolean;
  autoRevealAfterFirstTrick: boolean;
  exhaustedTrumpEnforced: boolean;
  communicationRestricted: boolean; // Always true in 304
  faceDownInFirstTrickOnly: boolean;
}
```

**Implementation:**
- **Chat Restrictions**: No verbal communication allowed during card play
- **Allowed Phrases**: Only basic game phrases ("Pass", "Bid", "Good game", "Thanks")
- **UI Enforcement**: Visual indicators show communication restrictions are active

### 4. **250+ Bid Special Rules Integration**

**Comprehensive Rule System:**
```typescript
export const get304SpecialBidRules = (finalBid: number): SpecialBidRules => {
  const is250Plus = finalBid >= 250;
  return {
    is250Plus,
    autoRevealAfterFirstTrick: is250Plus,
    exhaustedTrumpEnforced: is250Plus,
    communicationRestricted: true, // Always restricted in 304
    faceDownInFirstTrickOnly: is250Plus,
  };
};
```

## UI Enhancements

### 1. **Trump Maker Warnings**
- **250+ Bid Indicators**: Visual warnings for special rules when bidding 250+
- **Exhausted Trump Alerts**: Real-time warnings when trump maker must lead all trumps
- **Rule Reminders**: Context-sensitive help text for trump indicator card usage

### 2. **Communication Restrictions Display**
- **Visual Indicators**: Clear display of communication restrictions during play
- **Rule Explanations**: Tooltips explaining why certain communications are blocked

### 3. **Enhanced Trump Information**
- **Auto-Reveal Notifications**: Clear indication when trump is auto-revealed after first trick
- **Trump History**: Detailed log of trump reveals and rule applications

## Technical Implementation

### 1. **Validation Flow**
```
Card Play Request
    ↓
Trump Indicator Validation
    ↓
Exhausted Trump Validation (250+ bids)
    ↓
Enhanced Face-Down Logic
    ↓
Play Card with Rule Enforcement
```

### 2. **Auto-Reveal Logic**
```
Trick Completion
    ↓
Check if First Trick (250+ bids)
    ↓
Auto-Reveal Trump
    ↓
Update Trump History
    ↓
Reveal All Cards in Trick
```

### 3. **Communication Enforcement**
```
Game State Check
    ↓
Apply Communication Restrictions
    ↓
Filter Allowed Messages
    ↓
Display Restriction Warnings
```

## Testing Scenarios

### 1. **Exhausted Trump Rule**
- ✅ Trump maker has all remaining trumps → Must lead all trumps
- ✅ Trump maker plays non-trump when trumps remain → Validation error
- ✅ Rule only applies to 250+ bids → Lower bids unaffected

### 2. **Enhanced Face-Down Logic**
- ✅ 250+ bid, first trick → Face-down cards allowed
- ✅ 250+ bid, second trick → All cards face-up
- ✅ Lower bids → Original face-down logic preserved

### 3. **Auto-Reveal After First Trick**
- ✅ 250+ bid, first trick complete → Trump auto-revealed
- ✅ Trump history updated → Proper logging
- ✅ All first trick cards revealed → Enhanced visibility

## Impact on Game Flow

### **Positive Changes:**
1. **Rule Compliance**: Game now fully complies with official 304 rules for 250+ bids
2. **Strategic Depth**: Exhausted trump rule adds tactical consideration for high bids
3. **Clarity**: Auto-reveal after first trick reduces confusion in 250+ bid games
4. **Authenticity**: Communication restrictions maintain traditional 304 gameplay

### **Backwards Compatibility:**
- All existing games with bids under 250 continue to work exactly as before
- No breaking changes to existing functionality
- Enhanced features are additive, not replacement

## Future Enhancements

1. **Advanced Caps Detection**: Implement automatic caps detection when exhausted trumps guarantee all remaining tricks
2. **AI Opponent Logic**: Update AI to understand and utilize exhausted trump strategies
3. **Tournament Mode**: Add competitive mode with strict rule enforcement
4. **Rule Variants**: Support for regional 304 variants with different special rules

---

**Status**: ✅ **COMPLETED** - All critical 304 card playing and trick logic issues have been resolved.

**Priority**: 🟢 **HIGH PRIORITY FIXED** - Game now properly implements official 304 rules for advanced play. 