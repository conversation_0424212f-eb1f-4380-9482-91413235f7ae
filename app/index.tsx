import React, { useState } from 'react';
import { ImageBackground, StyleSheet } from 'react-native';
import GameBoard from '../components/GameBoard';
import GameLobby from '../components/GameLobby';

export default function Index() {
  const [gameState, setGameState] = useState<'lobby' | 'game'>('lobby');
  const [currentRoomId, setCurrentRoomId] = useState<string>('');
  const [currentPlayerId, setCurrentPlayerId] = useState<string>('');

  const handleGameStart = (roomId: string, playerId: string) => {
    console.log('📱 handleGameStart called with:', roomId, playerId);
    setCurrentRoomId(roomId);
    setCurrentPlayerId(playerId);
    setGameState('game');
    console.log('📱 Game state changed to: game');
  };

  const handleGameEnd = () => {
    setGameState('lobby');
    setCurrentRoomId('');
    setCurrentPlayerId('');
  };

  return (
    <>
      {/* <StatusBar hidden /> */}
      <ImageBackground 
        source={require('../assets/images/bg.png')} 
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        {/* <SafeAreaView style={styles.container}> */}
          {gameState === 'lobby' ? (
            <GameLobby onGameStart={handleGameStart} />
          ) : (
            <GameBoard 
              roomId={currentRoomId}
              playerId={currentPlayerId}
              onGameEnd={handleGameEnd}
            />
          )}
        {/* </SafeAreaView> */}
      </ImageBackground>
    </>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
});
