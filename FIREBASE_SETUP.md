# Firebase Setup Guide

This guide will help you set up Firebase for your multiplayer card game.

## Prerequisites

1. You need a Google account
2. Your Expo development environment should be ready

## Step 1: Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "card-game-multiplayer")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Set up Firestore Database

1. In your Firebase project console, click on "Firestore Database" in the left sidebar
2. Click "Create database"
3. Choose "Start in test mode" for development (you can change security rules later)
4. Select a location for your database (choose one close to your users)
5. Click "Done"

## Step 3: Register Your App

### For Web (Expo Web)
1. In your Firebase project console, click the Web icon (`</>`)
2. Enter an app nickname (e.g., "card-game-web")
3. Check "Also set up Firebase Hosting" if you plan to deploy to web
4. Click "Register app"
5. Copy the Firebase configuration object

### For iOS (if you plan to build for iOS)
1. Click the iOS icon
2. Enter your iOS bundle ID (from your `app.json` or `app.config.js`)
3. Download the `GoogleService-Info.plist` file
4. Follow the setup instructions

### For Android (if you plan to build for Android)
1. Click the Android icon
2. Enter your Android package name (from your `app.json` or `app.config.js`)
3. Download the `google-services.json` file
4. Follow the setup instructions

## Step 4: Configure Your Expo App

1. Open `config/firebase.ts` in your project
2. Replace the placeholder values with your actual Firebase configuration:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-actual-sender-id",
  appId: "your-actual-app-id"
};
```

## Step 5: Test Your Setup

1. Start your Expo development server:
   ```bash
   npx expo start
   ```

2. Open your app in Expo Go or a simulator
3. Try creating a room - if it works, your Firebase setup is correct!

## Step 6: Security Rules (Important for Production)

For development, we're using test mode, but for production, you should set up proper security rules.

In your Firebase console, go to Firestore Database > Rules and update them:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to game rooms
    match /gameRooms/{roomId} {
      allow read, write: if true; // Adjust this based on your security needs
    }
  }
}
```

## Troubleshooting

### Common Issues:

1. **"Firebase: No Firebase App '[DEFAULT]' has been created"**
   - Make sure your Firebase configuration is correct in `config/firebase.ts`
   - Ensure you've imported and initialized Firebase properly

2. **"Missing or insufficient permissions"**
   - Check your Firestore security rules
   - For development, you can use test mode rules

3. **"Firebase project not found"**
   - Verify your `projectId` in the configuration
   - Make sure the project exists in your Firebase console

4. **Network/Connection Issues**
   - Ensure you have internet connectivity
   - Check if Firebase services are down (rare)

## Game Features Powered by Firebase

Your game now includes:

- **Real-time multiplayer**: Players can join rooms and play together
- **Turn management**: Automated turn system
- **Card distribution**: Server-side card shuffling and distribution
- **Game state synchronization**: All players see the same game state
- **Room management**: Create, join, and leave game rooms
- **Host controls**: Room creator can start the game

## Next Steps

1. Test the multiplayer functionality with friends
2. Consider adding user authentication
3. Implement game scoring/statistics
4. Add more game features (chat, spectator mode, etc.)
5. Set up proper security rules for production deployment

Happy gaming! 🃏🎮 