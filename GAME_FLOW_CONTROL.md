# Enhanced Game Flow Control for 304 Card Game

## Overview

This enhanced game flow control system provides comprehensive round management, deal progression, and game state handling for the 304 card game. It includes automatic phase transitions, timeout management, dealer rotation, and detailed game state tracking.

## Key Features

### 1. Enhanced Game State Management

#### Round State Tracking
- **Round Number**: Current round being played
- **Deal Number**: Current deal within the round
- **Phase**: Current game phase (dealing, bidding, trump_selection, trump_type_selection, playing, scoring, finished)
- **Tricks Completed**: Number of completed tricks in current round
- **Total Tricks**: Total tricks per round (standard: 8)
- **Timing**: Phase start time and timeouts

#### Game Progress Tracking
- **Total Rounds**: Configurable number of rounds
- **Rounds Completed**: Number of completed rounds
- **Game Duration**: Total time since game started
- **Estimated Time Remaining**: Calculated based on average phase times

### 2. Dealer Rotation System

#### Automatic Dealer Progression
- **Current Dealer**: Player currently dealing
- **Dealer History**: Track of all previous dealers
- **Dealing Order**: Predefined order for dealer rotation

#### Features
- Automatic rotation after each round
- History tracking for fairness verification
- Configurable rotation patterns

### 3. Phase Transition Management

#### Automatic Phase Advancement
- **Auto-advance**: Optional automatic progression between phases
- **Phase Timeouts**: Configurable timeouts for each phase
- **Player Action Requirements**: Track when player actions are required
- **Pending Actions**: Queue of actions waiting to be completed

#### Supported Phases
1. **Dealing**: Cards are distributed to players
2. **Bidding**: Players make bids for trump selection
3. **Trump Selection**: Highest bidder selects trump card
4. **Trump Type Selection**: Choose trump game type (open, closed, etc.)
5. **Playing**: Card playing phase
6. **Scoring**: Round scoring and evaluation
7. **Finished**: Game completion

### 4. Timeout Management

#### Configurable Timeouts
- **Bidding Timeout**: Default 30 seconds
- **Playing Timeout**: Default 15 seconds
- **Trump Selection Timeout**: Default 20 seconds

#### Auto-action on Timeout
- **Bidding**: Auto-pass for current player
- **Playing**: Auto-play first valid card
- **Trump Selection**: Auto-select based on rules

### 5. State History and Logging

#### Comprehensive Logging
- **Timestamp**: When each action occurred
- **Phase**: Game phase during action
- **Action Type**: Type of action performed
- **Player ID**: Player who triggered action (if applicable)
- **Details**: Additional context and data

#### Action Types
- `game_started`: Game initialization
- `phase_advance`: Phase transition
- `new_deal`: New deal started
- `trick_completed`: Trick completion
- `auto_progress_toggled`: Auto-progress setting changed
- `timeout_settings_updated`: Timeout configurations changed
- `game_completed`: Game finished

## Implementation Details

### Core Interfaces

```typescript
interface RoundState {
  roundNumber: number;
  dealNumber: number;
  phase: 'dealing' | 'bidding' | 'trump_selection' | 'trump_type_selection' | 'playing' | 'scoring' | 'finished';
  tricksCompleted: number;
  totalTricks: number;
  roundStartTime: number;
  phaseStartTime: number;
  timeouts: Record<string, number>;
}

interface GameFlow {
  currentRound: RoundState;
  gameProgress: {
    totalRounds: number;
    roundsCompleted: number;
    estimatedTimeRemaining: number;
    gameStartTime: number;
  };
  dealerRotation: {
    currentDealer: string;
    dealerHistory: string[];
    dealingOrder: string[];
  };
  phaseTransitions: {
    autoAdvance: boolean;
    timeouts: Record<string, number>;
    requiresPlayerAction: boolean;
    pendingActions: string[];
  };
}

interface EnhancedGameRoom extends GameRoom {
  gameFlow: GameFlow;
  stateHistory: GameStateHistory[];
  autoProgressSettings: {
    enableAutoProgress: boolean;
    bidTimeout: number;
    playTimeout: number;
    trumpSelectionTimeout: number;
  };
  dealProgression: {
    cardsPerDeal: number;
    totalDeals: number;
    currentDeal: number;
    dealPattern: 'standard' | 'progressive' | 'custom';
  };
}
```

### Key Methods

#### Game Flow Control
- `initializeGameFlow()`: Initialize game flow state
- `advanceGamePhase()`: Manually advance to next phase
- `progressDeal()`: Handle automatic deal progression
- `completeRound()`: Handle round completion and scoring

#### Timeout Management
- `setupPhaseTimeout()`: Set up automatic timeouts
- `handlePhaseTimeout()`: Handle timeout actions
- `toggleAutoProgress()`: Enable/disable auto-progression
- `updatePhaseTimeouts()`: Update timeout settings

#### Scoring and Statistics
- `calculateRoundScores()`: Calculate 304 game scores
- `checkGameEndCondition()`: Check if game should end
- `getGameFlowStatistics()`: Get detailed game statistics

## Usage Examples

### Starting a Game with Enhanced Flow Control

```typescript
// Enhanced game start with flow control
await GameService.startGame(roomId, hostId);
// Automatically initializes:
// - Game flow state
// - Dealer rotation
// - Auto-progress settings
// - State history logging
```

### Manual Phase Advancement

```typescript
// Host can manually advance phases
await GameService.advanceGamePhase(roomId, 'playing', playerId);
```

### Enabling Auto-Progress

```typescript
// Toggle auto-progress for automatic phase transitions
await GameService.toggleAutoProgress(roomId, true);

// Update timeout settings
await GameService.updatePhaseTimeouts(roomId, {
  bidTimeout: 45, // 45 seconds for bidding
  playTimeout: 20, // 20 seconds for playing
  trumpSelectionTimeout: 30 // 30 seconds for trump selection
});
```

### Getting Game Statistics

```typescript
const stats = GameService.getGameFlowStatistics(gameRoom);
console.log('Average bidding time:', stats.averagePhaseTime.bidding);
console.log('Total game time:', stats.totalGameTime);
console.log('Dealer rotation counts:', stats.dealerRotationCounts);
```

## UI Components

### GameFlowControl Component

The `GameFlowControl` component provides a visual interface for:

#### Status Display
- Current round and deal numbers
- Current phase with countdown timer
- Progress bar showing trick completion
- Dealer information and score targets

#### Host Controls
- Toggle auto-progress on/off
- Force phase advancement
- View detailed flow information

#### Flow Details Modal
- Comprehensive game flow information
- Round and deal progress
- Dealer rotation history
- Auto-progress settings
- Game duration and statistics

## Integration with Existing Game Logic

### Bidding Integration
- Enhanced bidding phase with timeout support
- Automatic progression after bid completion
- Logging of all bidding actions

### Card Playing Integration
- Trick completion tracking
- Automatic round progression
- Enhanced scoring calculation

### Trump Selection Integration
- Timeout support for trump selection
- Multiple trump game types
- Progressive trump features

## Configuration Options

### Auto-Progress Settings
- **Enable/Disable**: Toggle automatic progression
- **Timeouts**: Configure phase timeouts
- **Actions**: Define auto-actions on timeout

### Game Parameters
- **Total Rounds**: Set number of rounds to play
- **Target Score**: Configure winning score
- **Deal Pattern**: Standard, progressive, or custom

### Logging and History
- **History Retention**: Keep last 100 entries
- **Detailed Logging**: All game actions tracked
- **Statistics**: Real-time game statistics

## Benefits

1. **Improved Game Flow**: Smooth transitions between phases
2. **Better User Experience**: Clear status indication and progress tracking
3. **Fairness**: Automatic dealer rotation and timeout management
4. **Statistics**: Detailed game analytics and history
5. **Flexibility**: Configurable settings for different play styles
6. **Reliability**: Robust error handling and state management

## Future Enhancements

1. **AI Player Integration**: Support for computer players
2. **Tournament Mode**: Multi-game tournament support
3. **Advanced Statistics**: More detailed analytics
4. **Custom Rules**: Support for rule variations
5. **Replay System**: Game replay functionality
6. **Performance Optimization**: Further optimization for large games

This enhanced game flow control system provides a comprehensive foundation for managing 304 card game sessions with professional-grade features and reliability. 