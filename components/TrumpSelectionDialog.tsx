import React, { useState } from 'react';
import { Animated, Dimensions, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, getCardById, getCardRanking } from '../services/gameService';

const { width, height } = Dimensions.get('window');

interface TrumpSelectionDialogProps {
  visible: boolean;
  playerName: string;
  cards: Card[];
  onSelectTrump: (cardId: string) => void;
  onClose: () => void;
  biddingType?: 'four_card' | 'eight_card';
}

export default function TrumpSelectionDialog({
  visible,
  playerName,
  cards,
  onSelectTrump,
  onClose,
  biddingType = 'four_card'
}: TrumpSelectionDialogProps) {
  console.log('TrumpSelectionDialog render - visible:', visible, 'playerName:', playerName);
  
  // Function to sort cards by suit and rank (highest to lowest, right to left)
  const sortCards = (cardsToSort: Card[]) => {
    if (!cardsToSort || cardsToSort.length === 0) return cardsToSort;

    // Define suit order (you can adjust this based on preference)
    const suitOrder = ['spades', 'hearts', 'diamonds', 'clubs'];

    // Sort cards by suit first, then by rank (highest to lowest)
    return [...cardsToSort].sort((a, b) => {
      const cardA = getCardById(a.id);
      const cardB = getCardById(b.id);

      // First sort by suit
      const suitIndexA = suitOrder.indexOf(cardA.suit);
      const suitIndexB = suitOrder.indexOf(cardB.suit);

      if (suitIndexA !== suitIndexB) {
        return suitIndexA - suitIndexB;
      }

      // If same suit, sort by rank (highest to lowest)
      const rankA = getCardRanking(cardA.rank);
      const rankB = getCardRanking(cardB.rank);

      return rankB - rankA; // Descending order (highest to lowest)
    });
  };

  // 304 RULE: Card selection depends on bidding type
  const availableCards = sortCards(biddingType === 'four_card' ? cards.slice(0, 4) : cards);
  
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [cardAnimations] = useState(
    availableCards.reduce((acc, card) => ({
      ...acc,
      [card.id]: new Animated.Value(1)
    }), {} as Record<string, Animated.Value>)
  );

  const cardWidth = Math.min((width - 160) / 4, 80);
  const cardHeight = cardWidth * 1.4;

  const handleCardPress = (cardId: string) => {
    try {
      console.log('Card selected:', cardId);
      setSelectedCardId(cardId);
      
      // Animate the selected card
      Object.keys(cardAnimations).forEach(id => {
        Animated.timing(cardAnimations[id], {
          toValue: id === cardId ? 1.1 : 0.95,
          duration: 200,
          useNativeDriver: true,
        }).start();
      });

      // Auto-confirm selection after a brief delay
      setTimeout(() => {
        onSelectTrump(cardId);
      }, 800);
    } catch (error) {
      console.error('Error in handleCardPress:', error);
    }
  };

  const getSuitSymbol = (suit: string) => {
    switch (suit) {
      case 'hearts': return '♥️';
      case 'diamonds': return '♦️';
      case 'clubs': return '♣️';
      case 'spades': return '♠️';
      default: return '';
    }
  };

  // Don't render if not visible - iOS crash prevention
  if (!visible) {
    console.log('TrumpSelectionDialog not visible, returning null');
    return null;
  }

  console.log('TrumpSelectionDialog rendering overlay (NO MODAL)');

  // Render as overlay without Modal component - following the working pattern
  return (
    <SafeAreaView style={styles.modalOverlay}>
      <View style={styles.modalContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <View style={styles.headerBadge}>
            <Text style={styles.headerBadgeText}>🏆 Trump Selection</Text>
          </View>
          {/* <Text style={styles.playerNameText}>
            {playerName || 'Player'}
          </Text> */}
          {/* <Text style={styles.instructionText}>
            {biddingType === 'four_card' 
              ? "Choose your trump card from your first 4 cards!"
              : "Choose your trump card from any of your 8 cards!"
            }{'\n'}The selected suit will dominate this round
          </Text> */}
        </View>

        {/* Cards Display */}
        <View style={styles.cardsSection}>
          <Text style={styles.cardsSectionTitle}>
            Select Your Trump Card
          </Text>
          
          <View style={styles.cardsContainer}>
            {availableCards.map((card, index) => {
              const cardImage = getCardById(card.id).image;
              const cardData = getCardById(card.id);
              const isSelected = selectedCardId === card.id;
              const animation = cardAnimations[card.id] || new Animated.Value(1);
              
              return (
                <Animated.View
                  key={card.id}
                  style={{
                    transform: [{ scale: animation }],
                  }}
                >
                  <TouchableOpacity
                    onPress={() => handleCardPress(card.id)}
                    style={[
                      styles.cardButton,
                      isSelected && styles.cardButtonSelected,
                      { width: cardWidth, height: cardHeight }
                    ]}
                    activeOpacity={0.8}
                  >
                    <Image
                      source={cardImage}
                      style={styles.cardImage}
                      resizeMode="contain"
                    />
                    
                    {/* Selected Overlay */}
                    {isSelected && (
                      <View style={styles.selectedOverlay}>
                        <View style={styles.selectedIndicator}>
                          <Text style={styles.selectedIcon}>✓</Text>
                        </View>
                      </View>
                    )}
                    
                    {/* Card Info Overlay */}
                    <View style={[
                      styles.cardInfoOverlay,
                      isSelected && styles.cardInfoOverlaySelected
                    ]}>
                      <Text style={[
                        styles.cardInfoText,
                        isSelected && styles.cardInfoTextSelected
                      ]}>
                        {cardData.rank} {getSuitSymbol(cardData.suit)}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              );
            })}
          </View>
        </View>

        {/* Bottom Card Info */}
        {/* <View style={styles.bottomInfoContainer}>
          <Text style={styles.bottomInfoTitle}>
            Card Selection Rules
          </Text>
          <Text style={styles.bottomInfoText}>
            • The suit of your selected card becomes trump{'\n'}
            • Trump cards beat all other suits{'\n'}
            • Higher trump cards beat lower trump cards
          </Text>
        </View> */}

        {selectedCardId && (
          <View style={styles.selectedCardInfo}>
            <View style={styles.selectedCardHeader}>
              <Text style={styles.selectedCardIcon}>🎯</Text>
              <Text style={styles.selectedCardText}>
                {getSuitSymbol(getCardById(selectedCardId).suit)} {getCardById(selectedCardId).suit.charAt(0).toUpperCase() + getCardById(selectedCardId).suit.slice(1)} Selected!
              </Text>
            </View>
          </View>
        )}

        {/* Cancel Button */}
        {/* <TouchableOpacity
          onPress={onClose}
          style={styles.cancelButton}
        >
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity> */}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: 'auto',
    height: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    zIndex: 1000,
    elevation: 1000,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 32,
    marginHorizontal: 16,
    maxWidth: width - 40,
    // Simplified shadow for iOS stability
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerBadge: {
    backgroundColor: '#3B82F6',
    borderRadius: 50,
    paddingHorizontal: 24,
    paddingVertical: 8,
    marginBottom: 16,
  },
  headerBadgeText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  playerNameText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
  },
  instructionText: {
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
    fontSize: 16,
    lineHeight: 24,
  },
  cardsSection: {
    marginBottom: 32,
  },
  cardsSectionTitle: {
    textAlign: 'center',
    color: '#374151',
    fontWeight: '600',
    marginBottom: 16,
    fontSize: 18,
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
  },
  cardButton: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 3,
    borderColor: 'transparent',
    position: 'relative',
  },
  cardButtonSelected: {
    borderColor: '#22C55E',
    // Simplified shadow for selected state
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  selectedOverlay: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#22C55E',
    borderRadius: 20,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedIndicator: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedIcon: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cardInfoOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: '#3B82F6',
    borderRadius: 4,
    paddingHorizontal: 4,
  },
  cardInfoOverlaySelected: {
    backgroundColor: '#F59E0B',
  },
  cardInfoText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardInfoTextSelected: {
    color: '#1F2937',
  },
  bottomInfoContainer: {
    backgroundColor: '#EFF6FF',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#BFDBFE',
    marginBottom: 16,
  },
  bottomInfoTitle: {
    color: '#1E40AF',
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  bottomInfoText: {
    color: '#1E40AF',
    fontSize: 14,
    lineHeight: 20,
  },
  selectedCardInfo: {
    backgroundColor: '#F0FDF4',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#BBF7D0',
    marginBottom: 16,
  },
  selectedCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedCardIcon: {
    color: '#16A34A',
    fontSize: 24,
    marginRight: 8,
  },
  selectedCardText: {
    color: '#15803D',
    fontWeight: 'bold',
    fontSize: 18,
  },
  cancelButton: {
    backgroundColor: '#f44336',
    padding: 15,
    borderRadius: 8,
    width: '100%',
  },
  cancelText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
  },
});