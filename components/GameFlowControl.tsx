import React, { useEffect, useState } from 'react';
import { Alert, Modal, Platform, Text, TouchableOpacity, View } from 'react-native';
import { EnhancedGameRoom, GameService, RoundState } from '../services/gameService';

interface GameFlowControlProps {
  gameRoom: EnhancedGameRoom;
  playerId: string;
  isHost: boolean;
}

export default function GameFlowControl({ gameRoom, playerId, isHost }: GameFlowControlProps) {
  const [showFlowDetails, setShowFlowDetails] = useState(false);
  const [phaseTimeRemaining, setPhaseTimeRemaining] = useState<number>(0);

  // Calculate time remaining in current phase
  useEffect(() => {
    if (!gameRoom.gameFlow?.currentRound?.phaseStartTime) return;

    const updateTimer = () => {
      const currentPhase = gameRoom.gameFlow.currentRound.phase;
      const phaseStartTime = gameRoom.gameFlow.currentRound.phaseStartTime;
      const timeoutDuration = gameRoom.gameFlow.phaseTransitions.timeouts[currentPhase] || 30000;
      
      const elapsed = Date.now() - phaseStartTime;
      const remaining = Math.max(0, timeoutDuration - elapsed);
      
      setPhaseTimeRemaining(Math.floor(remaining / 1000));
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [gameRoom.gameFlow?.currentRound?.phaseStartTime, gameRoom.gameFlow?.currentRound?.phase]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPhaseDisplayName = (phase: RoundState['phase']): string => {
    switch (phase) {
      case 'dealing': return 'Dealing Cards';
      case 'bidding': return 'Bidding';
      case 'trump_selection': return 'Trump Selection';
      case 'trump_type_selection': return 'Trump Type Selection';
      case 'in_progress': return 'Playing';
      case 'scoring': return 'Scoring';
      case 'finished': return 'Finished';
      default: return phase;
    }
  };

  const handleToggleAutoProgress = async () => {
    if (!isHost) {
      Alert.alert('Permission Denied', 'Only the host can change auto-progress settings');
      return;
    }

    try {
      await GameService.toggleAutoProgress(gameRoom.id, !gameRoom.autoProgressSettings.enableAutoProgress);
    } catch (error) {
      Alert.alert('Error', 'Failed to toggle auto-progress');
    }
  };

  const handleForceAdvancePhase = async () => {
    if (!isHost) {
      Alert.alert('Permission Denied', 'Only the host can force phase advancement');
      return;
    }

    const currentPhase = gameRoom.gameFlow.currentRound.phase;
    let nextPhase: RoundState['phase'];

    switch (currentPhase) {
      case 'dealing':
        nextPhase = 'bidding';
        break;
      case 'bidding':
        nextPhase = 'trump_selection';
        break;
      case 'trump_selection':
        nextPhase = 'trump_type_selection';
        break;
      case 'trump_type_selection':
        nextPhase = 'in_progress';
        break;
      case 'in_progress':
        nextPhase = 'scoring';
        break;
      case 'scoring':
        nextPhase = 'dealing'; // Next round
        break;
      default:
        Alert.alert('Cannot Advance', 'Current phase cannot be advanced');
        return;
    }

    try {
      await GameService.advanceGamePhase(gameRoom.id, nextPhase, playerId);
      Alert.alert('Success', `Advanced to ${getPhaseDisplayName(nextPhase)}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to advance phase');
    }
  };

  const getCurrentDealerName = (): string => {
    const currentDealer = gameRoom.players.find(p => p.id === gameRoom.gameFlow?.dealerRotation?.currentDealer);
    return currentDealer?.name || 'Unknown';
  };

  const getProgressPercentage = (): number => {
    if (!gameRoom.gameFlow?.currentRound) return 0;
    
    const { tricksCompleted, totalTricks } = gameRoom.gameFlow.currentRound;
    return Math.floor((tricksCompleted / totalTricks) * 100);
  };

  return (
    <View className="bg-gray-800 p-4 rounded-lg border border-gray-600">
      {/* Main Flow Status */}
      <View className="flex-row justify-between items-center mb-3">
        <View>
          <Text className="text-white font-bold text-lg">
            Round {gameRoom.gameFlow?.currentRound?.roundNumber || 1}
          </Text>
          <Text className="text-gray-300 text-sm">
            Deal {gameRoom.gameFlow?.currentRound?.dealNumber || 1}
          </Text>
        </View>
        
        <View className="items-end">
          <Text className="text-blue-400 font-semibold">
            {getPhaseDisplayName(gameRoom.gameFlow?.currentRound?.phase || 'dealing')}
          </Text>
          {gameRoom.autoProgressSettings?.enableAutoProgress && phaseTimeRemaining > 0 && (
            <Text className="text-yellow-400 text-sm">
              {formatTime(phaseTimeRemaining)}
            </Text>
          )}
        </View>
      </View>

      {/* Progress Bar */}
      <View className="mb-3">
        <View className="bg-gray-600 h-2 rounded-full">
          <View 
            className="bg-blue-500 h-2 rounded-full"
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </View>
        <Text className="text-gray-400 text-xs mt-1">
          Tricks: {gameRoom.gameFlow?.currentRound?.tricksCompleted || 0} / {gameRoom.gameFlow?.currentRound?.totalTricks || 8}
        </Text>
      </View>

      {/* Dealer Info */}
      <View className="flex-row justify-between items-center mb-3">
        <Text className="text-gray-300">Dealer: <Text className="text-white">{getCurrentDealerName()}</Text></Text>
        <Text className="text-gray-300">
          Score Target: <Text className="text-white">{gameRoom.gameScore}</Text>
        </Text>
      </View>

      {/* Control Buttons */}
      <View className="flex-row justify-between">
        <TouchableOpacity
          onPress={() => setShowFlowDetails(true)}
          className="bg-blue-600 px-4 py-2 rounded"
        >
          <Text className="text-white text-sm">Details</Text>
        </TouchableOpacity>

        {isHost && (
          <View className="flex-row space-x-2">
            <TouchableOpacity
              onPress={handleToggleAutoProgress}
              className={`px-4 py-2 rounded ${
                gameRoom.autoProgressSettings?.enableAutoProgress 
                  ? 'bg-green-600' 
                  : 'bg-gray-600'
              }`}
            >
              <Text className="text-white text-sm">
                Auto: {gameRoom.autoProgressSettings?.enableAutoProgress ? 'ON' : 'OFF'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleForceAdvancePhase}
              className="bg-orange-600 px-4 py-2 rounded"
            >
              <Text className="text-white text-sm">Force</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Flow Details Modal */}
      <Modal
        visible={showFlowDetails}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFlowDetails(false)}
        // iOS Crash Prevention: Additional modal props
        presentationStyle="overFullScreen"
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          padding: 16,
        }}>
          <View style={{
            backgroundColor: '#1F2937',
            padding: 24,
            borderRadius: 8,
            maxHeight: 384,
          }}>
            <Text className="text-white text-xl font-bold mb-4">Game Flow Details</Text>
            
            {/* Round Information */}
            <View className="mb-4">
              <Text className="text-blue-400 font-semibold mb-2">Current Round</Text>
              <Text className="text-gray-300">Round: {gameRoom.gameFlow?.currentRound?.roundNumber}</Text>
              <Text className="text-gray-300">Deal: {gameRoom.gameFlow?.currentRound?.dealNumber}</Text>
              <Text className="text-gray-300">Phase: {getPhaseDisplayName(gameRoom.gameFlow?.currentRound?.phase || 'dealing')}</Text>
              <Text className="text-gray-300">Tricks: {gameRoom.gameFlow?.currentRound?.tricksCompleted} / {gameRoom.gameFlow?.currentRound?.totalTricks}</Text>
            </View>

            {/* Game Progress */}
            <View className="mb-4">
              <Text className="text-blue-400 font-semibold mb-2">Game Progress</Text>
              <Text className="text-gray-300">
                Rounds Completed: {gameRoom.gameFlow?.gameProgress?.roundsCompleted} / {gameRoom.gameFlow?.gameProgress?.totalRounds}
              </Text>
              <Text className="text-gray-300">
                Game Duration: {gameRoom.gameFlow?.gameProgress?.gameStartTime ? 
                  formatTime(Math.floor((Date.now() - gameRoom.gameFlow.gameProgress.gameStartTime) / 1000)) : '0:00'}
              </Text>
            </View>

            {/* Dealer Rotation */}
            <View className="mb-4">
              <Text className="text-blue-400 font-semibold mb-2">Dealer Rotation</Text>
              <Text className="text-gray-300">Current: {getCurrentDealerName()}</Text>
              <Text className="text-gray-300">
                History: {gameRoom.gameFlow?.dealerRotation?.dealerHistory?.length || 0} deals
              </Text>
            </View>

            {/* Auto-Progress Settings */}
            <View className="mb-6">
              <Text className="text-blue-400 font-semibold mb-2">Auto-Progress Settings</Text>
              <Text className="text-gray-300">
                Enabled: {gameRoom.autoProgressSettings?.enableAutoProgress ? 'Yes' : 'No'}
              </Text>
              <Text className="text-gray-300">
                Bid Timeout: {gameRoom.autoProgressSettings?.bidTimeout}s
              </Text>
              <Text className="text-gray-300">
                Play Timeout: {gameRoom.autoProgressSettings?.playTimeout}s
              </Text>
              <Text className="text-gray-300">
                Trump Selection Timeout: {gameRoom.autoProgressSettings?.trumpSelectionTimeout}s
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => setShowFlowDetails(false)}
              className="bg-blue-600 p-3 rounded"
            >
              <Text className="text-white text-center font-semibold">Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
} 