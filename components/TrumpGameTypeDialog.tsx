import React from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TrumpGameType } from '../services/gameService';

interface TrumpGameTypeDialogProps {
  visible: boolean;
  playerName: string;
  onSelectGameType: (gameType: TrumpGameType) => void;
  onClose: () => void;
}

// Enhanced trump options with better styling
const TRUMP_OPTIONS = [
  { 
    type: 'open', 
    title: 'Open Trump', 
    icon: '🔓',
    description: 'All cards visible',
    color: '#4CAF50',
    gradient: ['#4CAF50', '#45a049']
  },
  { 
    type: 'closed', 
    title: 'Closed Trump', 
    icon: '🔒',
    description: 'Hidden trump card',
    color: '#FF9800',
    gradient: ['#FF9800', '#f57c00']
  },
  // { 
  //   type: 'no_trump', 
  //   title: 'No Trump', 
  //   icon: '🚫',
  //   description: 'No trump suit',
  //   color: '#f44336',
  //   gradient: ['#f44336', '#d32f2f']
  // }
];

export default function TrumpGameTypeDialog({
  visible,
  playerName,
  onSelectGameType,
  onClose
}: TrumpGameTypeDialogProps) {
  console.log('TrumpDialog render - visible:', visible, 'playerName:', playerName);

  const handleSelect = (gameType: string) => {
    try {
      console.log('Trump type selected:', gameType);
      onSelectGameType(gameType as TrumpGameType);
    } catch (error) {
      console.error('Error in handleSelect:', error);
    }
  };

  // Don't render if not visible
  if (!visible) {
    console.log('TrumpDialog not visible, returning null');
    return null;
  }

  console.log('TrumpDialog rendering overlay (NO MODAL)');

  // Render as enhanced overlay
  return (
    <SafeAreaView style={styles.overlay}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.playerName}>
            🎯 {playerName || 'Player'}
          </Text>
          <Text style={styles.title}>
            Choose Trump
          </Text>
          <Text style={styles.subtitle}>
            Select trump type for this round
          </Text>
        </View>
        
        <View style={styles.optionsContainer}>
          {TRUMP_OPTIONS.map((option, index) => (
            <TouchableOpacity
              key={option.type}
              onPress={() => handleSelect(option.type)}
              style={[styles.optionButton, { backgroundColor: option.color }]}
              activeOpacity={0.8}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionIcon}>{option.icon}</Text>
                <View style={styles.optionTextContainer}>
                  <Text style={styles.optionTitle}>{option.title}</Text>
                  <Text style={styles.optionDescription}>{option.description}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        
        {/* <TouchableOpacity
          onPress={onClose}
          style={styles.cancelButton}
          activeOpacity={0.7}
        >
          <Text style={styles.cancelText}>✕ Cancel</Text>
        </TouchableOpacity> */}
      </View>
    </SafeAreaView>
  );
}

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: 'auto',
    height: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 1000,
    elevation: 1000,
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 0,
    width: '100%',
    maxWidth: 320,
    maxHeight: height * 0.8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 16,
    overflow: 'hidden',
  },
  header: {
    backgroundColor: '#1a237e',
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  playerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 4,
    opacity: 0.9,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 13,
    color: '#ffffff',
    opacity: 0.8,
    textAlign: 'center',
  },
  optionsContainer: {
    padding: 16,
    gap: 10,
  },
  optionButton: {
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    marginBottom: 1,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 1,
  },
  optionDescription: {
    color: 'white',
    fontSize: 12,
    opacity: 0.9,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginHorizontal: 16,
    marginBottom: 16,
    paddingVertical: 12,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
  },
  cancelText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '600',
  },
});