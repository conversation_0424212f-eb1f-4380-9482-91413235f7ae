import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, Modal, Platform, ScrollView, Text, TouchableOpacity, View } from 'react-native';

interface Bid {
  playerId: string;
  amount: number;
  isPassed: boolean;
  timestamp: number;
  bidRound: number;
}

interface BiddingDialogProps {
  visible: boolean;
  currentBid: number;
  playerName: string;
  bids: Bid[];
  biddingHistory: string[];
  highestBidder?: string;
  players: { id: string; name: string }[];
  onSubmitBid: (bidAmount: number) => void;
  onPass: () => void;
  onClose: () => void;
}

export default function BiddingDialog({
  visible,
  currentBid,
  playerName,
  bids,
  biddingHistory,
  highestBidder,
  players,
  onSubmitBid,
  onPass,
  onClose
}: BiddingDialogProps) {
  const [selectedBid, setSelectedBid] = useState<number | null>(null);

  // Enhanced debug logging for iOS
  useEffect(() => {
    if (Platform.OS === 'ios') {
      console.log('BiddingDialog iOS - Props Debug:', {
        visible,
        playerName: playerName || 'MISSING/EMPTY',
        playerNameType: typeof playerName,
        players: players || 'MISSING',
        playersIsArray: Array.isArray(players),
        playersLength: players?.length || 'N/A',
        currentBid,
        highestBidder: highestBidder || 'none'
      });
    }
  }, [visible, playerName, players, currentBid, highestBidder]);

  // Reset selected bid when dialog becomes invisible
  useEffect(() => {
    if (!visible) {
      setSelectedBid(null);
      if (Platform.OS === 'ios') {
        console.log('BiddingDialog iOS - resetting selectedBid');
      }
    }
  }, [visible]);

  // Memoize expensive calculations to prevent re-renders
  const bidOptions = useMemo(() => {
    try {
      const minBid = Math.max(160, Math.ceil((currentBid + 1) / 10) * 10);
      // First bidding round (4-card bidding) is limited to 200 max according to 304 rules
      const maxBid = 200;
      const options = [];
      
      for (let bid = minBid; bid <= maxBid; bid += 10) {
        options.push(bid);
      }
      
      if (Platform.OS === 'ios') {
        console.log('BiddingDialog iOS - generated bid options:', options.length);
      }
      
      return options;
    } catch (error) {
      console.error('Error generating bid options:', error);
      return [];
    }
  }, [currentBid]);
  
  // Memoize highest bidder name with better error handling
  const highestBidderName = useMemo(() => {
    try {
      if (!highestBidder || !Array.isArray(players) || players.length === 0) {
        return 'None yet';
      }
      
      const bidder = players.find(p => p?.id === highestBidder);
      return bidder?.name || 'Unknown';
    } catch (error) {
      console.error('Error getting highest bidder name:', error);
      return 'Unknown';
    }
  }, [highestBidder, players]);

  // Enhanced bid validation - 304 Official Rules
  const validateSelectedBid = useCallback((bidAmount: number): { isValid: boolean; error?: string } => {
    try {
      if (bidAmount < 160) {
        return { isValid: false, error: 'Minimum bid is 160' };
      }
      // First bidding round (4-card bidding) is limited to 200 max
      if (bidAmount > 200) {
        return { isValid: false, error: 'Maximum bid for first round is 200' };
      }
      if (bidAmount % 10 !== 0) {
        return { isValid: false, error: 'Bids must be in multiples of 10' };
      }
      if (bidAmount <= currentBid) {
        return { isValid: false, error: `Bid must be higher than ${currentBid}` };
      }
      return { isValid: true };
    } catch (error) {
      console.error('Error validating bid:', error);
      return { isValid: false, error: 'Validation error' };
    }
  }, [currentBid]);

  const handleBidSelect = useCallback((bidAmount: number) => {
    try {
      if (Platform.OS === 'ios') {
        console.log('BiddingDialog iOS - bid selected:', bidAmount);
      }
      
      if (!visible) {
        Alert.alert('Error', 'Bidding is no longer available');
        return;
      }

      const validation = validateSelectedBid(bidAmount);
      if (!validation.isValid) {
        Alert.alert('Invalid Bid', validation.error || 'Invalid bid amount');
        return;
      }

      onSubmitBid(bidAmount);
    } catch (error) {
      console.error('Error selecting bid:', error);
    }
  }, [visible, validateSelectedBid, onSubmitBid]);

  const handlePassSelect = useCallback(() => {
    try {
      if (Platform.OS === 'ios') {
        console.log('BiddingDialog iOS - pass selected');
      }
      
      if (!visible) {
        Alert.alert('Error', 'Bidding is no longer available');
        return;
      }
      
      onPass();
    } catch (error) {
      console.error('Error selecting pass:', error);
    }
  }, [visible, onPass]);

  const handleAskPartner = useCallback(() => {
    Alert.alert('Ask Partner', 'This feature allows you to communicate with your partner during bidding.');
  }, []);

  const handleModalClose = useCallback(() => {
    try {
      if (Platform.OS === 'ios') {
        console.log('BiddingDialog iOS - modal close requested');
      }
    } catch (error) {
      console.error('Error handling modal close:', error);
    }
  }, []);

  // Enhanced safety check with detailed logging
  if (!visible) {
    if (Platform.OS === 'ios') {
      console.log('BiddingDialog iOS - not visible, not rendering');
    }
    return null;
  }

  if (!playerName || typeof playerName !== 'string' || playerName.trim() === '') {
    if (Platform.OS === 'ios') {
      console.log('BiddingDialog iOS - playerName invalid:', { 
        playerName, 
        type: typeof playerName,
        isEmpty: !playerName || playerName.trim() === ''
      });
    }
    return null;
  }

  if (!players || !Array.isArray(players) || players.length === 0) {
    if (Platform.OS === 'ios') {
      console.log('BiddingDialog iOS - players invalid:', { 
        players, 
        isArray: Array.isArray(players),
        length: players?.length 
      });
    }
    return null;
  }

  // Additional safety check for players array content
  const validPlayers = players.filter(p => p && typeof p === 'object' && p.id && p.name);
  if (validPlayers.length === 0) {
    if (Platform.OS === 'ios') {
      console.log('BiddingDialog iOS - no valid players found:', players);
    }
    return null;
  }

  if (Platform.OS === 'ios') {
    console.log('BiddingDialog iOS - all safety checks passed, rendering modal');
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleModalClose}
      supportedOrientations={['landscape', 'portrait']}
      presentationStyle={Platform.OS === 'ios' ? 'pageSheet' : 'fullScreen'}
    >
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        paddingHorizontal: 10,
        paddingVertical: 20,
      }}>
        <View style={{
          backgroundColor: 'white',
          borderRadius: 12,
          width: '95%',
          maxWidth: 600,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}>
          {/* Header */}
          <View style={{ paddingHorizontal: 16, paddingTop: 16, paddingBottom: 8 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              textAlign: 'center',
              color: '#1f2937'
            }}>
              {playerName}, It&apos;s Your Turn to Bid
            </Text>
          </View>

          {/* Bidding Information */}
          <View style={{
            backgroundColor: '#f9fafb',
            marginHorizontal: 16,
            borderRadius: 8,
            padding: 10,
            marginBottom: 16
          }}>
            <Text style={{
              textAlign: 'center',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: 2,
              fontSize: 14
            }}>
              Current Bid: {currentBid > 0 ? currentBid : 'No bids yet'}
            </Text>
            <Text style={{
              textAlign: 'center',
              color: '#6b7280',
              fontSize: 12
            }}>
              Highest Bidder: {highestBidderName}
            </Text>
          </View>

          {/* Bidding History - Compact */}
          {Array.isArray(biddingHistory) && biddingHistory.length > 0 && (
            <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
              <Text style={{
                fontSize: 12,
                fontWeight: '600',
                color: '#374151',
                marginBottom: 6,
                textAlign: 'center'
              }}>
                Recent Bids:
              </Text>
              <View style={{
                backgroundColor: '#f9fafb',
                borderRadius: 6,
                padding: 6,
                maxHeight: 40
              }}>
                <ScrollView 
                  showsVerticalScrollIndicator={false}
                  bounces={false}
                  horizontal={true}
                >
                  <Text style={{
                    fontSize: 11,
                    color: '#6b7280',
                    textAlign: 'center'
                  }}>
                    {biddingHistory.slice(-3).join(' • ')}
                  </Text>
                </ScrollView>
              </View>
            </View>
          )}

          {/* Bid Options Grid */}
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: 8
            }}>
              {/* Bid Values */}
              {bidOptions.map((bidAmount) => (
                <TouchableOpacity
                  key={`bid-option-${bidAmount}`}
                  onPress={() => handleBidSelect(bidAmount)}
                  style={{
                    backgroundColor: '#60a5fa',
                    borderRadius: 8,
                    paddingVertical: 12,
                    paddingHorizontal: 10,
                    minWidth: 70,
                    height: 50,
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                    elevation: 2,
                  }}
                  activeOpacity={0.8}
                >
                  <Text style={{
                    color: 'white',
                    fontSize: 16,
                    fontWeight: 'bold'
                  }}>
                    {bidAmount}
                  </Text>
                </TouchableOpacity>
              ))}
              
              {/* PASS Button */}
              <TouchableOpacity
                onPress={handlePassSelect}
                style={{
                  backgroundColor: '#60a5fa',
                  borderRadius: 8,
                  paddingVertical: 12,
                  paddingHorizontal: 10,
                  minWidth: 70,
                  height: 50,
                  alignItems: 'center',
                  justifyContent: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.1,
                  shadowRadius: 2,
                  elevation: 2,
                }}
                activeOpacity={0.8}
              >
                <Text style={{
                  color: 'white',
                  fontSize: 14,
                  fontWeight: 'bold'
                }}>
                  PASS
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Ask Partner Button */}
          {/* <View style={{ paddingHorizontal: 16, paddingBottom: 16 }}>
            <TouchableOpacity
              onPress={handleAskPartner}
              style={{
                backgroundColor: '#60a5fa',
                borderRadius: 8,
                paddingVertical: 12,
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.1,
                shadowRadius: 2,
                elevation: 2,
              }}
              activeOpacity={0.8}
            >
              <Text style={{
                color: 'white',
                fontSize: 16,
                fontWeight: 'bold'
              }}>
                Ask Partner
              </Text>
            </TouchableOpacity>
          </View> */}
        </View>
      </View>
    </Modal>
  );
}