import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Dimensions, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { GameRoom, GameService } from '../services/gameService';

interface GameLobbyProps {
  onGameStart: (roomId: string, playerId: string) => void;
}

export default function GameLobby({ onGameStart }: GameLobbyProps) {
  const [playerId] = useState(() => Math.random().toString(36).substring(7)); // Generate random player ID
  const [playerName, setPlayerName] = useState('');
  const [roomId, setRoomId] = useState('');
  const [currentRoom, setCurrentRoom] = useState<GameRoom | null>(null);
  const [availableRooms, setAvailableRooms] = useState<GameRoom[]>([]);
  const [showRoomBrowser, setShowRoomBrowser] = useState(false);
  const [loading, setLoading] = useState(false);
  const [unsubscribe, setUnsubscribe] = useState<(() => void) | null>(null);
  const [roomsUnsubscribe, setRoomsUnsubscribe] = useState<(() => void) | null>(null);

  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  const isSmallScreen = screenWidth < 380;
  const isMediumScreen = screenWidth >= 380 && screenWidth < 768;
  const isLargeScreen = screenWidth >= 768;

  useEffect(() => {
    // Cleanup subscription on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      if (roomsUnsubscribe) {
        roomsUnsubscribe();
      }
    };
  }, [unsubscribe, roomsUnsubscribe]);

  // Load available rooms when toggling room browser
  useEffect(() => {
    if (showRoomBrowser) {
      const unsub = GameService.subscribeToAvailableRooms((rooms) => {
        setAvailableRooms(rooms);
      });
      setRoomsUnsubscribe(() => unsub);
    } else {
      if (roomsUnsubscribe) {
        roomsUnsubscribe();
        setRoomsUnsubscribe(null);
      }
      setAvailableRooms([]);
    }
  }, [showRoomBrowser]);

  // Monitor current room state changes and trigger game start
  useEffect(() => {
    if (currentRoom && currentRoom.gameState !== 'waiting') {
      console.log('🎮 Current room state changed to:', currentRoom.gameState);
      console.log('🚀 Calling onGameStart from useEffect');
      onGameStart(currentRoom.id, playerId);
    }
  }, [currentRoom?.gameState]);

  const createRoom = async () => {
    if (!playerName.trim()) {
      Alert.alert('Error', 'Please enter your name');
      return;
    }

    setLoading(true);
    try {
      const newRoomId = await GameService.createRoom(playerId, playerName.trim());
      setRoomId(newRoomId);
      
      // Subscribe to room updates
      const unsub = GameService.subscribeToRoom(newRoomId, (room) => {
        console.log('🏠 Room update in createRoom:', room?.gameState);
        setCurrentRoom(room);
        if (room?.gameState !== 'waiting') {
          console.log('🚀 Calling onGameStart from createRoom');
          onGameStart(newRoomId, playerId);
        }
      });
      setUnsubscribe(() => unsub);
      
      Alert.alert('Success', `Room created! Room ID: ${newRoomId}`);
    } catch (error) {
      console.error('Error creating room:', error);
      Alert.alert('Error', 'Failed to create room');
    } finally {
      setLoading(false);
    }
  };

  const joinRoom = async () => {
    if (!playerName.trim()) {
      Alert.alert('Error', 'Please enter your name');
      return;
    }

    if (!roomId.trim()) {
      Alert.alert('Error', 'Please enter room ID');
      return;
    }

    setLoading(true);
    try {
      await GameService.joinRoom(roomId.trim(), playerId, playerName.trim());
      
      // Subscribe to room updates
      const unsub = GameService.subscribeToRoom(roomId.trim(), (room) => {
        console.log('🏠 Room update in joinRoom:', room?.gameState);
        setCurrentRoom(room);
        if (room?.gameState !== 'waiting') {
          console.log('🚀 Calling onGameStart from joinRoom');
          onGameStart(roomId.trim(), playerId);
        }
      });
      setUnsubscribe(() => unsub);
      
      Alert.alert('Success', 'Joined room successfully!');
    } catch (error: any) {
      console.error('Error joining room:', error);
      Alert.alert('Error', error.message || 'Failed to join room');
    } finally {
      setLoading(false);
    }
  };

  const startGame = async () => {
    if (!currentRoom) return;

    setLoading(true);
    try {
      await GameService.startGame(currentRoom.id, playerId);
    } catch (error: any) {
      console.error('Error starting game:', error);
      Alert.alert('Error', error.message || 'Failed to start game');
    } finally {
      setLoading(false);
    }
  };

  const leaveRoom = async () => {
    if (!currentRoom) return;

    try {
      await GameService.leaveRoom(currentRoom.id, playerId);
      if (unsubscribe) {
        unsubscribe();
        setUnsubscribe(null);
      }
      setCurrentRoom(null);
      setRoomId('');
    } catch (error) {
      console.error('Error leaving room:', error);
    }
  };

  const selectRoom = (selectedRoomId: string) => {
    setRoomId(selectedRoomId);
    setShowRoomBrowser(false);
  };

  const toggleRoomBrowser = () => {
    setShowRoomBrowser(!showRoomBrowser);
  };

  // Responsive styles
  const containerPadding = isSmallScreen ? 'p-4' : isMediumScreen ? 'p-5' : 'p-6';
  const cardPadding = isSmallScreen ? 'p-4' : isMediumScreen ? 'p-5' : 'p-6';
  const titleSize = isSmallScreen ? 'text-xl' : isMediumScreen ? 'text-2xl' : 'text-3xl';
  const textSize = isSmallScreen ? 'text-sm' : 'text-base';
  const buttonPadding = isSmallScreen ? 'py-2.5 px-4' : 'py-3 px-6';
  const inputPadding = isSmallScreen ? 'px-3 py-2.5' : 'px-4 py-3';

  if (currentRoom) {
    const isHost = currentRoom.hostId === playerId;
    const canStartGame = isHost && currentRoom.players.length === 4;

    return (
      <SafeAreaView className="flex-1 bg-gray-100">
        <ScrollView 
          className={`flex-1 ${containerPadding}`}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          <View className={`bg-white rounded-lg ${cardPadding} shadow-lg ${isLargeScreen ? 'max-w-2xl mx-auto w-full' : ''}`}>
            <Text className={`${titleSize} font-bold text-center mb-4 text-gray-800`}>Game Lobby</Text>
            
            {/* Room Info Card */}
            <View className="bg-blue-50 rounded-lg p-4 mb-6 border-l-4 border-blue-400">
              <Text className={`${isSmallScreen ? 'text-base' : 'text-lg'} font-semibold text-center mb-2 text-gray-800`}>
                Room ID: {currentRoom.id}
              </Text>
              <Text className={`text-center ${textSize} text-gray-600`}>
                Players: {currentRoom.players.length}/{currentRoom.maxPlayers}
              </Text>
            </View>

            {/* Players List */}
            <View className="mb-6">
              <Text className={`${isSmallScreen ? 'text-base' : 'text-lg'} font-semibold mb-3 text-gray-800`}>Players:</Text>
              <View className="space-y-2">
                {currentRoom.players.map((player, index) => (
                  <View key={player.id} className="flex-row justify-between items-center py-3 px-4 bg-gray-50 rounded-lg border border-gray-200">
                    <View className="flex-1">
                      <Text className={`${textSize} font-medium text-gray-800`}>{player.name}</Text>
                      <Text className="text-xs text-gray-500 mt-1">Position: {player.position}</Text>
                    </View>
                    <View className="flex-row items-center space-x-2">
                      {player.isHost && (
                        <View className="bg-blue-100 px-2 py-1 rounded-full">
                          <Text className="text-xs font-semibold text-blue-800">Host</Text>
                        </View>
                      )}
                      <View className="w-3 h-3 bg-green-400 rounded-full"></View>
                    </View>
                  </View>
                ))}
                
                {/* Empty slots */}
                {Array.from({ length: currentRoom.maxPlayers - currentRoom.players.length }).map((_, index) => (
                  <View key={`empty-${index}`} className="flex-row justify-between items-center py-3 px-4 bg-gray-100 rounded-lg border border-gray-200 border-dashed">
                    <Text className={`${textSize} text-gray-400 italic`}>Waiting for player...</Text>
                    <View className="w-3 h-3 bg-gray-300 rounded-full"></View>
                  </View>
                ))}
              </View>
            </View>

            {/* Game Status */}
            <View className="bg-yellow-50 rounded-lg p-4 mb-6 border-l-4 border-yellow-400">
              <Text className={`text-center ${textSize} font-medium text-gray-700`}>
                Status: {
                  currentRoom.gameState === 'waiting' ? 'Waiting for players' :
                  currentRoom.gameState === 'bidding' ? 'Bidding phase' :
                  currentRoom.gameState === 'trump_selection' ? 'Trump selection' :
                  currentRoom.gameState === 'in_progress' ? 'Game in progress' :
                  currentRoom.gameState === 'finished' ? 'Game finished' :
                  'Game in progress'
                }
              </Text>
            </View>

            {/* Action Buttons */}
            <View className="space-y-3">
              {canStartGame && (
                <TouchableOpacity
                  onPress={startGame}
                  disabled={loading}
                  className={`bg-green-500 ${buttonPadding} rounded-lg shadow-sm ${loading ? 'opacity-50' : ''}`}
                  activeOpacity={0.8}
                >
                  {loading ? (
                    <ActivityIndicator color="white" />
                  ) : (
                    <Text className={`text-white text-center font-semibold ${textSize}`}>Start Game</Text>
                  )}
                </TouchableOpacity>
              )}

              <TouchableOpacity
                onPress={leaveRoom}
                className={`bg-red-500 ${buttonPadding} rounded-lg shadow-sm`}
                activeOpacity={0.8}
              >
                <Text className={`text-white text-center font-semibold ${textSize}`}>Leave Room</Text>
              </TouchableOpacity>
            </View>

            {isHost && currentRoom.players.length < 4 && (
              <View className="mt-6 bg-gray-50 rounded-lg p-4">
                <Text className={`text-center ${isSmallScreen ? 'text-xs' : 'text-sm'} text-gray-500`}>
                  Need exactly 4 players for 304 game ({currentRoom.players.length}/4)
                </Text>
              </View>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <View className="flex-1 bg-gray-100">
      <ScrollView 
        className={`flex-1 ${containerPadding}`}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <View className={`bg-white rounded-lg ${cardPadding} shadow-lg ${isLargeScreen ? 'max-w-2xl mx-auto w-full' : ''}`}>
          <Text className={`${titleSize} font-bold text-center mb-6 text-gray-800`}>Join or Create Game</Text>

          {/* Player Name Input */}
          <View className="mb-6">
            <Text className={`${isSmallScreen ? 'text-base' : 'text-lg'} font-semibold mb-3 text-gray-800`}>Your Name:</Text>
            <TextInput
              value={playerName}
              onChangeText={setPlayerName}
              placeholder="Enter your name"
              className={`border border-gray-300 rounded-lg ${inputPadding} ${textSize} bg-white`}
              maxLength={20}
              placeholderTextColor="#9CA3AF"
            />
          </View>

          {/* Room ID Input with Browser */}
          <View className="mb-6">
            <View className="flex-row justify-between items-center mb-3">
              <Text className={`${isSmallScreen ? 'text-base' : 'text-lg'} font-semibold text-gray-800`}>Room ID (to join):</Text>
              <TouchableOpacity
                onPress={toggleRoomBrowser}
                className="bg-gray-200 px-3 py-2 rounded-lg"
                activeOpacity={0.7}
              >
                <Text className={`${isSmallScreen ? 'text-xs' : 'text-sm'} text-gray-700 font-medium`}>
                  {showRoomBrowser ? 'Hide Rooms' : 'Browse Rooms'}
                </Text>
              </TouchableOpacity>
            </View>
            
            <TextInput
              value={roomId}
              onChangeText={setRoomId}
              placeholder="Enter room ID or select from list below"
              className={`border border-gray-300 rounded-lg ${inputPadding} ${textSize} bg-white`}
              autoCapitalize="none"
              placeholderTextColor="#9CA3AF"
            />

            {/* Available Rooms List */}
            {showRoomBrowser && (
              <View className="mt-4 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                <View className="flex-row justify-between items-center p-4 bg-gray-100 border-b border-gray-200">
                  <Text className={`${isSmallScreen ? 'text-sm' : 'text-base'} font-semibold text-gray-800`}>Available Rooms:</Text>
                  <View className="bg-blue-100 px-2 py-1 rounded-full">
                    <Text className="text-xs font-medium text-blue-800">
                      {availableRooms.length} room{availableRooms.length !== 1 ? 's' : ''}
                    </Text>
                  </View>
                </View>
                
                <ScrollView 
                  className="max-h-64" 
                  showsVerticalScrollIndicator={true}
                  nestedScrollEnabled={true}
                >
                  {availableRooms.length === 0 ? (
                    <View className="p-8 items-center">
                      <Text className={`${textSize} text-gray-500 text-center mb-2`}>No available rooms found</Text>
                      <Text className="text-xs text-gray-400 text-center">Create a room or wait for others to create one</Text>
                    </View>
                  ) : (
                    <View className="p-2">
                      {availableRooms.map((room) => (
                        <TouchableOpacity
                          key={room.id}
                          onPress={() => selectRoom(room.id)}
                          className={`flex-row justify-between items-center p-3 rounded-lg mb-2 border ${
                            roomId === room.id 
                              ? 'bg-blue-100 border-blue-300' 
                              : 'bg-white border-gray-200'
                          }`}
                          activeOpacity={0.7}
                        >
                          <View className="flex-1 pr-3">
                            <Text className={`font-semibold ${textSize} text-gray-800`}>
                              Room: {room.id.substring(0, 8)}...
                            </Text>
                            <Text className={`${isSmallScreen ? 'text-xs' : 'text-sm'} text-gray-600 mt-1`}>
                              Host: {room.players.find(p => p.isHost)?.name || 'Unknown'}
                            </Text>
                            <Text className="text-xs text-gray-500 mt-1">
                              Created: {room.createdAt ? new Date(room.createdAt.seconds * 1000).toLocaleTimeString() : 'Unknown'}
                            </Text>
                          </View>
                          <View className="items-end">
                            <View className="bg-blue-100 px-2 py-1 rounded-full mb-1">
                              <Text className="text-sm font-semibold text-blue-700">
                                {room.players.length}/{room.maxPlayers}
                              </Text>
                            </View>
                            {room.players.length >= room.maxPlayers && (
                              <Text className="text-xs text-red-500 font-medium">Full</Text>
                            )}
                            {roomId === room.id && (
                              <Text className="text-xs text-blue-600 font-semibold">✓ Selected</Text>
                            )}
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </ScrollView>
                
                                 <View className="p-3 bg-gray-100 border-t border-gray-200">
                   <Text className="text-xs text-gray-500 text-center">
                     Tap a room to select it, then click &quot;Join Room&quot;
                   </Text>
                 </View>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View className="space-y-4">
            <TouchableOpacity
              onPress={createRoom}
              disabled={loading}
              className={`bg-blue-500 ${buttonPadding} rounded-lg shadow-sm ${loading ? 'opacity-50' : ''}`}
              activeOpacity={0.8}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text className={`text-white text-center font-semibold ${textSize}`}>Create New Room</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              onPress={joinRoom}
              disabled={loading}
              className={`bg-green-500 ${buttonPadding} rounded-lg shadow-sm ${loading ? 'opacity-50' : ''}`}
              activeOpacity={0.8}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text className={`text-white text-center font-semibold ${textSize}`}>Join Room</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
} 