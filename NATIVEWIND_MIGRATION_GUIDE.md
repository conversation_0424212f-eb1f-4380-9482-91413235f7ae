# NativeWind to Native StyleSheet Migration Guide

## Overview

This guide helps identify and migrate components using NativeWind that may cause iOS crashes, particularly those involving Modal components or complex layouts.

## Components Requiring Immediate Attention

Based on the codebase analysis, the following components use NativeWind with Modal and should be migrated to prevent iOS crashes:

### 1. **TrumpGameTypeDialog.tsx** ⚠️ HIGH PRIORITY
- **Issue**: Modal with complex NativeWind className usage
- **Risk**: iOS crash during trump selection phase
- **NativeWind Usage**: 
  ```tsx
  <View className="flex-1 justify-center items-center bg-black/50 p-4">
    <View className="bg-white rounded-lg p-6 w-full max-w-md">
  ```

### 2. **TrumpSelectionDialog.tsx** ⚠️ HIGH PRIORITY  
- **Issue**: Modal with gradient and complex layouts
- **Risk**: iOS crash during trump card selection
- **NativeWind Usage**:
  ```tsx
  <View className="flex-1 justify-center items-center">
    <View className="bg-white rounded-3xl p-8 mx-4 shadow-2xl">
  ```

### 3. **GameFlowControl.tsx** ⚠️ MEDIUM PRIORITY
- **Issue**: Modal with complex background and layout classes
- **Risk**: iOS crash when opening game details
- **NativeWind Usage**:
  ```tsx
  <View className="flex-1 bg-black/50 justify-center p-4">
    <View className="bg-gray-800 p-6 rounded-lg max-h-96">
  ```

## Migration Process

### Step 1: Identify NativeWind in Modal Components
Search for files containing both `Modal` and `className`:
```bash
grep -l "Modal" components/*.tsx | xargs grep -l "className"
```

### Step 2: Convert className to StyleSheet

#### Before (NativeWind):
```tsx
<Modal visible={visible} transparent={true}>
  <View className="flex-1 justify-center items-center bg-black/50">
    <View className="bg-white rounded-lg p-6 w-80">
      <Text className="text-xl font-bold text-center mb-4">
        Title Text
      </Text>
    </View>
  </View>
</Modal>
```

#### After (Native StyleSheet):
```tsx
<Modal visible={visible} transparent={true}>
  <View style={styles.modalOverlay}>
    <View style={styles.modalContainer}>
      <Text style={styles.title}>
        Title Text
      </Text>
    </View>
  </View>
</Modal>

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 24,
    width: 320,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
});
```

### Step 3: Common NativeWind to StyleSheet Conversions

| NativeWind Class | StyleSheet Equivalent |
|------------------|----------------------|
| `flex-1` | `flex: 1` |
| `justify-center` | `justifyContent: 'center'` |
| `items-center` | `alignItems: 'center'` |
| `bg-black/50` | `backgroundColor: 'rgba(0, 0, 0, 0.5)'` |
| `bg-white` | `backgroundColor: 'white'` |
| `rounded-lg` | `borderRadius: 8` |
| `p-6` | `padding: 24` |
| `mb-4` | `marginBottom: 16` |
| `text-xl` | `fontSize: 20` |
| `font-bold` | `fontWeight: 'bold'` |
| `text-center` | `textAlign: 'center'` |

### Step 4: Add iOS-Specific Optimizations

```tsx
<Modal
  visible={visible}
  transparent={true}
  animationType="slide"
  // iOS Crash Prevention
  presentationStyle="overFullScreen"
  statusBarTranslucent={Platform.OS === 'android'}
>
```

## Testing Checklist

After migration, test each component:

- [ ] **iOS Simulator**: No crashes on modal open/close
- [ ] **iOS Device**: Test on actual device with different iOS versions
- [ ] **Rapid Interactions**: Test quick successive modal opens
- [ ] **Memory Pressure**: Test during long gameplay sessions
- [ ] **Background/Foreground**: Test app lifecycle transitions

## Priority Migration Order

1. **Immediate** (iOS crash risk):
   - `BiddingDialog.tsx` ✅ **COMPLETED**
   - `TrumpGameTypeDialog.tsx` ✅ **COMPLETED**
   - `TrumpSelectionDialog.tsx` ✅ **COMPLETED**

2. **High Priority** (potential issues):
   - `GameFlowControl.tsx` Modal sections ✅ **COMPLETED**

3. **Medium Priority** (preventive):
   - Complex layout components in `GameBoard.tsx`
   - `GameLobby.tsx` if Modal usage added

## Benefits After Migration

- ✅ **Eliminates iOS crashes** related to NativeWind Modal conflicts
- ✅ **Improves performance** by removing style processing overhead  
- ✅ **Increases reliability** with predictable native styling
- ✅ **Reduces bundle size** by removing unused NativeWind dependencies
- ✅ **Better debugging** with direct style inspection

## Alternative Solutions

If maintaining utility-first styling is critical:

1. **Tamagui**: Better iOS compatibility than NativeWind
2. **react-native-unistyles**: Performance-focused alternative
3. **Styled Components**: Runtime styling with better iOS support
4. **Native StyleSheet**: Most reliable for critical components

## Monitoring

After migration, monitor:
- iOS crash reports (should decrease significantly)
- App performance metrics
- User feedback on UI responsiveness
- Memory usage during modal operations

This migration ensures stable iOS performance while maintaining visual consistency and user experience. 